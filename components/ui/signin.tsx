import React, { useEffect, FC } from 'react'; // Import FC (Functional Component) for typing
import { X } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { signIn } from "next-auth/react";

// Define the props interface for LoginModal
interface LoginModalProps {
  onClose: () => void; // onClose is a function that takes no arguments and returns void
}

// Use FC<LoginModalProps> to type the functional component
const LoginModal: FC<LoginModalProps> = ({ onClose }) => {
  console.log("NEXTAUTH_URL:", process.env.NEXTAUTH_URL);

  // Close modal on ESC key
  useEffect(() => {
    // Explicitly type the 'e' parameter as KeyboardEvent
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    // Add event listener to the window
    window.addEventListener('keydown', handleEsc);

    // Cleanup function: remove the event listener when the component unmounts
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [onClose]); // Dependency array: ensures the effect re-runs if onClose function reference changes

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm px-4">
      <div className="relative w-full max-w-xl rounded-2xl bg-white p-4 shadow-2xl text-center">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 transition"
          aria-label="Close"
        >
          <X size={20} />
        </button>

        {/* Heading */}
        <h2 className="text-2xl font-bold text-gray-900">
          Your AI-Powered Resume Deserves <br />
          the Spotlight!
        </h2>

        {/* Robot Image */}
        <div className="my-4">
          <img
            src={`${process.env.NEXT_PUBLIC_BASE_URL}/assets/img/category/icon1.png`} // Assuming this image is in your public directory
            alt="AI Bot with Resume"
            className="mx-auto h-40"
          />
        </div>

        {/* Description */}
        <p className="text-base font-normal text-gray-600 mb-6">
          Sign in to save your progress and continue crafting a resume that stands out.
        </p>

        {/* Sign-in button */}
        <Button 
            variant="login"
            size="none"
            onClick={() =>  signIn("google", { callbackUrl: "/ai-resume/profile" })}
            className=""
        >
          <img
            src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg"
            alt="Google Logo"
            className="h-5 w-5"
          />
          Sign in with Google
        </Button>
      </div>
    </div>
  );
};

export default LoginModal;