import NextAuth, { AuthOptions } from 'next-auth'
import GoogleProvider from 'next-auth/providers/google'

export const authOptions: AuthOptions = {
  session: {
    strategy: 'jwt'
  },
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async signIn({profile}) {
      if(!profile?.email){
        throw new Error('No Profile!');
      }
      return true;
    }
  },
  pages: {
    signIn: '/ai-resume/signin',
    error: '/ai-resume/error',
    // add others if needed
  },

}

export default NextAuth(authOptions)
