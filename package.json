{"name": "resumeitnow", "version": "0.2.0", "private": true, "scripts": {"dev": "npx next dev -p 8888", "build": "npx next build", "start": "npx next start -p 8888", "lint": "npx next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@mdxeditor/editor": "^3.14.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@react-pdf/renderer": "^4.1.6", "@sparticuz/chromium-min": "^131.0.1", "bootstrap": "^5.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "firebase": "^10.14.1", "framer-motion": "^12.5.0", "lodash": "^4.17.21", "lucide-react": "^0.453.0", "next": "14.2.15", "next-auth": "^4.24.8", "next-themes": "^0.3.0", "pdf-parse": "^1.1.1", "puppeteer": "^23.10.4", "puppeteer-core": "^23.10.4", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-countup": "^6.5.3", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.53.1", "react-icons": "^5.3.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/pdf-parse": "^1.1.4", "@types/react": "^18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}