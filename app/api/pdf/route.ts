import { NextResponse, type <PERSON>Request } from "next/server";
import puppeteer<PERSON><PERSON>, { type <PERSON>rowser as <PERSON>rowser<PERSON><PERSON> } from 'puppeteer-core';

// The path where Chromium is installed via 'apk' in the Alpine Docker image
const CHROMIUM_PATH = '/usr/bin/chromium-browser';

export const dynamic = 'force-dynamic';
export const maxDuration = 60;

export async function GET(request: NextRequest) {
    const { searchParams } = new URL(request.url);

    if (!searchParams.toString()) {
        return NextResponse.json({ 
            message: 'No query parameters provided' 
        }, { status: 400 });
    }

    let browser: BrowserCore | null = null;
    try {
        // Launch Puppeteer using the system-installed Chromium
        browser = await puppeteerCore.launch({
            executablePath: CHROMIUM_PATH,
            // These args are crucial for running in a Docker container
            args: [
            	'--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage', // Recommended for Docker
                '--single-process', // May improve performance in some container environments
                '--disable-gpu', // Often recommended in headless environments
                '--disable-software-rasterizer', // Can help with rendering issues
	    ],
            headless: true,
        });

        const page = await browser.newPage();
        
        // Construct URL using the exact same query parameters
        const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
        const url = new URL(`${baseUrl}/resume/download`);
        url.search = searchParams.toString();

        await page.goto(url.toString(), { waitUntil: 'networkidle0' });
        
        // Wait for the resume-content div to appear
        await page.waitForSelector('#resume-content', { 
            visible: true,
            timeout: 30000
        });

        const pdf = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
                top: '20px',
                right: '10px',
                bottom: '20px',
                left: '10px',
            },
        });

        return new NextResponse(pdf, {
            status: 200,
            headers: {
                'Content-Type': 'application/pdf',
                'Content-Disposition': `attachment; filename=resume.pdf`,
            },
        });
    } catch (error) {
        console.error('PDF generation error:', error);
        return NextResponse.json(
            { message: 'Error generating PDF' },
            { status: 500 }
        );
    } finally {
        // Ensure the browser is closed even if an error occurs
        if (browser) {
            await browser.close();
        }
    }
}
