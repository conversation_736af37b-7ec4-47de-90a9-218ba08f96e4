import './globals.css';
import { ThemeWrapper } from '@/components/ThemeWrapper';
import { getServerSession } from 'next-auth';
import SessionProvider from '@/components/SessionProvider';
import Script from 'next/script';




export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await getServerSession();
  
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href={`${process.env.NEXT_PUBLIC_BASE_URL}/favicon.ico`} />

        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        {/* <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css"/> */}

        <link href="https://fonts.googleapis.com/css2?family=Mona+Sans:ital,wght@0,200..900;1,200..900&display=swap" rel="stylesheet"/>
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-HKD0PBS6TT"
          strategy="afterInteractive"
          async
        />
        <Script id="gtag-init" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-HKD0PBS6TT');
          `}
        </Script>
      </head>
      <body >
        

          <SessionProvider session={session}>
            <ThemeWrapper>
              {children}
            </ThemeWrapper>
          </SessionProvider>
        
        <Script
          src="https://cdnjs.cloudflare.com/ajax/libs/jstimezonedetect/1.0.6/jstz.min.js"
          strategy="lazyOnload"
          // onLoad={() => console.log('timezonedetect loaded')}
        />
        <Script
          src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"
          strategy="afterInteractive"
        />
        <Script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js"
          strategy="afterInteractive"
        />
        <Script
          src={`${process.env.NEXT_PUBLIC_BASE_URL}/assets/js/theme.js`}
          // strategy="afterInteractive"
          // id="my-custom-script"              // Optional: Add an ID for debugging or specific targeting
          // onLoad={() => console.log('Custom script loaded!')} // Optional: Callback when script loads
        />
        {/* <script language="javascript">
                
          jQuery(document).ready(function($) {
                $('.carousel-1').slick({
                  dots: true,
                  infinite: true,
                  speed: 500,
                  slidesToShow: 3,
                  slidesToScroll: 1,
                  autoplay: true,
                  autoplaySpeed: 2000,
                  arrows: false
                  
                  
          });
          });
        </script> */}
      </body>
    </html>
  )
}