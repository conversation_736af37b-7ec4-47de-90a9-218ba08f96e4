"use client";
import { useState } from 'react'
import { Button } from "@/components/ui/button"
import LoginModal from "@/components/ui/signin"
import { useSession } from 'next-auth/react';
import { useRouter } from "next/navigation";

import type { FC } from 'react'



const FeatureCard: FC<{
  image: string;
  imageTitle: string;
  title: string;
  detail: string;
}> = ({ image, imageTitle, title, detail }) => (
    <div className="col-lg-3 col-sm-6 mb-6">
      <div className="card service-card shadow-hover rounded-3 text-center align-items-center">
        <div className="card-body p-xxl-5 p-4 relative flex flex-col justify-center items-center">
          <img src={`/ai-resume/${image}`}  alt={imageTitle} />
          <h4 className="mb-3 stress fw-bold">{title}</h4>
          <p className="mb-0 fw-medium">{detail}</p>
        </div>
      </div>
    </div>
);

export default function HomePage() {
  const [LoginModalisOpen, setLoginModalIsOpen] = useState(false);
  const {data:session} = useSession();
  const router = useRouter();


  return (
    <main className="main" id="top">

      {/* Top Banner */}
      <section className="no-padding" id='home'>
        <div className="bg-holder" style={{ backgroundImage:`url(${process.env.NEXT_PUBLIC_BASE_URL}/assets/img/hero/hero-bg.svg)` }}> </div>
        <div className="container">
          <div className="row align-items-center1">
            <div className="col-md-7 col-lg-6 text-md-start text-center">
              <h1 className="hero-title">Create a Smarter,<br /><span className='red-text !inline-block'>Free-to-Use</span> Resume with the Power of AI</h1>
              <span className="fw-bold text-xl"><span className='red-text'>Free to Use.</span> ATS-Optimised. Designed for Results. </span>
              <p className="mb-4 fw-medium">Let advanced AI craft your perfect resume. Get past hiring filters, showcase your strengths, and unlock more interview calls. All in just a few minutes. </p>
              <div className="text-center text-md-start">
                <Button
                  variant="default"
                  onClick={()=>{
                    if (session) {
                      router.push('/profile');
                    } else {
                      setLoginModalIsOpen(true);
                    }
                  }}
                  className='py-4 font-bold text-lg'
                >
                 BUILD MY RESUME
                </Button>
              </div>
      
            </div>
            <div className="col-md-5 col-lg-6 order-0 order-md-1 text-end"><img className="pt-7 pt-md-0 hero-img" src={`${process.env.NEXT_PUBLIC_BASE_URL}/assets/img/hero/hero-img.png`} alt="hero-header" /></div>
          </div>
        </div>
      </section>

      {/* Features Block Section */}
      <section className="need" id="service">
        <div className="container">
          <div className="position-absolute z-index--1 end-0 d-none d-lg-block">
            <img src={`${process.env.NEXT_PUBLIC_BASE_URL}/assets/img/category/shape.svg`}style={{ maxWidth: '200px' }} alt="service" />
            </div>
          <div className="mb-7 text-center">
            <h3 className="fs-xl-10 fs-lg-8 fs-7 fw-bold font-cursive text-capitalize">Everything you need to <span className="red-text">succeed</span> </h3>
            <p className="fw-medium py-3 succeed">Build a resume that gets noticed. From intelligent content suggestions to sleek, professional designs, our AI-powered platform gives you everything to get hired faster. </p>
          </div>
          <div className="row">
            <FeatureCard
              image="assets/img/category/icon1.png"
              imageTitle="Service"
              title="AI-Powered Content"
              detail="Struggling with what to write? Our AI turns your experience into impactful bullet points and summaries that recruiters can’t miss. No guesswork needed."
            />
            <FeatureCard
              image="assets/img/category/icon2.png"
              imageTitle="Service"
              title="Professional Templates"
              detail="Choose from expert-approved templates that are clean, modern, and optimised for applicant tracking systems (ATS). Make a strong first impression instantly."
            />
            <FeatureCard
              image="assets/img/category/icon3.png"
              imageTitle="Service"
              title="Export Your Resume Effortlessly"
              detail="Download a ready-to-send, high-quality PDF in just one click. Perfectly formatted for job portals, inboxes, and career pages."
            />
            <FeatureCard
              image="assets/img/category/icon4.png"
              imageTitle="Service"
              title="Privacy First"
              detail="Your personal data stays secure with bank-level encryption. We never sell, share, or store your information beyond what’s necessary to help you build your resume."
            />
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonial">
        <div className="container">
          <div className="row">
            <div className="col-lg-5">
              <div className="mb-8 text-start">
                <h3 className="fs-xl-10 fs-lg-8 fs-7 fw-bold font-cursive text-capitalize">Loved by <span className="red-text">professionals</span> </h3>
                <p className="mb-4 fw-medium py-3">See what our users say about their experience with our resume builder. </p>
              </div>
            </div>
            <div className="col-lg-1"></div>
            <div className="col-lg-6">
              <div className="pe-7 ps-5 ps-lg-0">
                <div className="carousel-1 slide carousel-fade position-static" id="testimonial" data-bs-ride="carousel"> 
                  <div className="carousel-inner">
                    <div className="carousel-item position-relative active">
                      <div className="card shadow" style={{ borderRadius:'10px' }}>
                        <div className="position-absolute start-0 top-0 translate-middle"> <img className="rounded-circle fit-cover" src={`${process.env.NEXT_PUBLIC_BASE_URL}/assets/img/testimonial/author1.png`} height="65" width="65" alt="" /></div>
                        <div className="card-body p-4">
                          <p className="fw-medium mb-4">&quot;This platform made resume writing so simple. The AI suggestions helped me present my experience better. I got more interview calls within a week!.&quot;</p>
                          <h5 className="text-secondary">Aarav Mehta</h5>
                          <p className="fw-medium fs--1 mb-0">Marketing Executive, Mumbai</p>
                        </div>
                      </div>
                      <div className="card shadow-sm position-absolute top-0 z-index--1 mb-3 w-100 h-100" style={{ borderRadius:'10px', transform:`translate(25px, 25px)` }}> </div>
                    </div>
                    <div className="carousel-item position-relative ">
                      <div className="card shadow" style={{ borderRadius:'10px' }}>
                        <div className="position-absolute start-0 top-0 translate-middle"> <img className="rounded-circle fit-cover" src={`${process.env.NEXT_PUBLIC_BASE_URL}/assets/img/testimonial/author2.png`} height="65" width="65" alt="" /></div>
                        <div className="card-body p-4">
                          <p className="fw-medium mb-4">&quot;As someone who’s not great at writing about myself, this tool was exactly what I needed. The AI gave me thoughtful suggestions that made my resume sound more polished and confident.&quot;</p>
                          <h5 className="text-secondary">Sneha Reddy </h5>
                          <p className="fw-medium fs--1 mb-0">Software Engineer, Hyderabad</p>
                        </div>
                      </div>
                      <div className="card shadow-sm position-absolute top-0 z-index--1 mb-3 w-100 h-100" style={{ borderRadius:'10px', transform:`translate(25px, 25px)` }}> </div>
                    </div>
                    <div className="carousel-item position-relative ">
                      <div className="card shadow" style={{ borderRadius:'10px' }}>
                        <div className="position-absolute start-0 top-0 translate-middle"> <img className="rounded-circle fit-cover" src={`${process.env.NEXT_PUBLIC_BASE_URL}/assets/img/testimonial/author3.png`} height="65" width="65" alt="" /></div>
                        <div className="card-body p-4">
                          <p className="fw-medium mb-4">&quot;The resume builder is clean, fast, and smart. It helped me rewrite my old resume in a much better format. The AI improved my bullet points and made everything easier to read. I shared the PDF with a hiring manager, and they immediately complimented the structure.&quot;</p>
                          <h5 className="text-secondary">Rohan Kapoor</h5>
                          <p className="fw-medium fs--1 mb-0">Data Analyst, Bengaluru</p>
                        </div>
                      </div>
                      <div className="card shadow-sm position-absolute top-0 z-index--1 mb-3 w-100 h-100" style={{ borderRadius:'10px', transform:`translate(25px, 25px)` }}> </div>
                    </div>
                    <div className="carousel-item position-relative ">
                      <div className="card shadow" style={{ borderRadius:'10px' }}>
                        <div className="position-absolute start-0 top-0 translate-middle"> <img className="rounded-circle fit-cover" src={`${process.env.NEXT_PUBLIC_BASE_URL}/assets/img/testimonial/author4.png`} height="65" width="65" alt="" /></div>
                        <div className="card-body p-4">
                          <p className="fw-medium mb-4">&quot;This resume builder saved me hours of formatting. The guidance it gave felt like working with a real career coach!.&quot;</p>
                          <h5 className="text-secondary">Neha Sharma</h5>
                          <p className="fw-medium fs--1 mb-0">HR Associate, Delhi</p>
                        </div>
                      </div>
                      <div className="card shadow-sm position-absolute top-0 z-index--1 mb-3 w-100 h-100" style={{ borderRadius:'10px', transform:`translate(25px, 25px)` }}> </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Get Started Section */}
      <section className="build" id='resume'>
        <div className="container ">
          <div className="position-absolute z-index--1 end-0 d-none d-lg-block"><img src={`${process.env.NEXT_PUBLIC_BASE_URL}/assets/img/category/shape.svg`} style={{ maxWidth: '200px' }} alt="service" /></div>
          <div className="text-center">
            <div className="row">
              <div className="col-md-6"> <img className="hero-img2" src={`${process.env.NEXT_PUBLIC_BASE_URL}/assets/img/hero-img3.png`}/></div>
              <div className="col-md-5 hero-img3-text">
                <h3 className="fs-xl-10 fs-lg-8 fs-7 fw-bold font-cursive text-capitalize">Ready to Build Your <span className="red-text">Resume ?</span></h3>
                <p className="mb-4 fw-medium ">Get started for free. Let AI polish your content, format your resume, and help you stand out in a competitive job market. </p>
                <span className="stress fw-bold">No stress. No cost. Just results.</span>
                <div className="text-center">
                  <Button
                    variant="default"
                    onClick={()=>{
                      if (session) {
                        router.push('/profile');
                      } else {
                        setLoginModalIsOpen(true);
                      }
                    }}
                    className='py-4 font-bold text-base'
                  >
                    MAKE MY RESUME
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div>
        {LoginModalisOpen && <LoginModal onClose={() => setLoginModalIsOpen(false)} />}
      </div>

    </main>

  );
}